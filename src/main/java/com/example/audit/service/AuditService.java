package com.example.audit.service;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.entity.AuditDetail;
import com.example.audit.entity.AuditLog;
import com.example.audit.entity.AuditableEntity;
import com.example.audit.repository.AuditLogRepository;
import com.example.audit.util.AuditContext;
import com.example.audit.util.JsonUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Core audit service responsible for capturing and persisting entity changes.
 *
 * This service provides field-level change tracking by comparing entity states
 * and creating detailed audit records. It supports asynchronous processing
 * to minimize impact on application performance.
 *
 * <AUTHOR> Agent
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {

    private final AuditLogRepository auditLogRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Value("${audit.enabled:true}")
    private boolean auditEnabled;

    @Value("${audit.async:true}")
    private boolean asyncAudit;

    @Value("${audit.max-field-length:1000}")
    private int maxFieldLength;

    @Value("#{'${audit.excluded-fields:password,token,secret}'.split(',')}")
    private Set<String> excludedFields;

    /**
     * Audit entity creation
     *
     * @param entity the created entity
     */
    public void auditCreate(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditCreateAsync(entity);
        } else {
            doAuditCreate(entity);
        }
    }

    /**
     * Audit entity update
     *
     * @param entity the updated entity
     */
    public void auditUpdate(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditUpdateAsync(entity);
        } else {
            doAuditUpdate(entity);
        }
    }

    /**
     * Audit entity update with original entity state
     *
     * @param entity the updated entity
     * @param originalEntity the original entity state before update
     */
    public void auditUpdate(Object entity, Object originalEntity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditUpdateAsync(entity, originalEntity);
        } else {
            doAuditUpdate(entity, originalEntity);
        }
    }

    /**
     * Audit entity deletion
     *
     * @param entity the deleted entity
     */
    public void auditDelete(Object entity) {
        if (!auditEnabled) {
            return;
        }

        if (asyncAudit) {
            auditDeleteAsync(entity);
        } else {
            doAuditDelete(entity);
        }
    }

    @Async
    public void auditCreateAsync(Object entity) {
        doAuditCreate(entity);
    }

    @Async
    public void auditUpdateAsync(Object entity) {
        doAuditUpdate(entity);
    }

    @Async
    public void auditUpdateAsync(Object entity, Object originalEntity) {
        doAuditUpdate(entity, originalEntity);
    }

    @Async
    public void auditDeleteAsync(Object entity) {
        doAuditDelete(entity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditCreate(Object entity) {
        try {
            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.CREATE);

            // For CREATE operations, all non-null fields are considered "new"
            List<AuditDetail> details = extractFieldDetails(entity, null, entity);
            details.forEach(auditLog::addDetail);

            auditLogRepository.save(auditLog);
            log.debug("Audited CREATE operation for entity: {} with ID: {}",
                     auditLog.getEntityName(), auditLog.getEntityId());

        } catch (Exception e) {
            log.error("Failed to audit CREATE operation: {}", e.getMessage(), e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditUpdate(Object entity) {
        doAuditUpdate(entity, null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditUpdate(Object entity, Object originalEntity) {
        try {
            log.debug("Starting UPDATE audit for entity: {} with original entity: {}",
                     entity.getClass().getSimpleName(), originalEntity != null ? "present" : "null");

            // If no original entity provided, try to get it from database
            if (originalEntity == null) {
                originalEntity = getOriginalEntity(entity);
                log.debug("Retrieved original entity from database: {}", originalEntity != null ? "present" : "null");
            }

            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.UPDATE);

            if (originalEntity != null) {
                // Compare original and current states
                List<AuditDetail> details = extractFieldDetails(entity, originalEntity, entity);
                log.debug("Extracted {} field details for UPDATE audit", details.size());

                // Only save if there are actual changes
                if (!details.isEmpty()) {
                    details.forEach(auditLog::addDetail);
                    auditLogRepository.save(auditLog);
                    log.debug("Audited UPDATE operation for entity: {} with ID: {} ({} changes)",
                             auditLog.getEntityName(), auditLog.getEntityId(), details.size());
                } else {
                    log.debug("No changes detected for UPDATE audit, skipping save");
                }
            } else {
                // No original entity available, create a simple UPDATE audit log
                log.debug("Creating UPDATE audit without field comparison");

                // Add a generic detail indicating an update occurred
                AuditDetail detail = AuditDetail.builder()
                    .fieldName("_update_")
                    .fieldType("UPDATE")
                    .oldValue("unknown")
                    .newValue("entity_updated")
                    .isSensitive(false)
                    .build();

                auditLog.addDetail(detail);
                auditLogRepository.save(auditLog);
                log.debug("Audited UPDATE operation for entity: {} with ID: {} (no field comparison)",
                         auditLog.getEntityName(), auditLog.getEntityId());
            }

        } catch (Exception e) {
            log.error("Failed to audit UPDATE operation: {}", e.getMessage(), e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void doAuditDelete(Object entity) {
        try {
            AuditLog auditLog = createAuditLog(entity, AuditLog.AuditOperation.DELETE);

            // For DELETE operations, all current fields are considered "removed"
            List<AuditDetail> details = extractFieldDetails(entity, entity, null);
            details.forEach(auditLog::addDetail);

            auditLogRepository.save(auditLog);
            log.debug("Audited DELETE operation for entity: {} with ID: {}",
                     auditLog.getEntityName(), auditLog.getEntityId());

        } catch (Exception e) {
            log.error("Failed to audit DELETE operation: {}", e.getMessage(), e);
        }
    }

    private AuditLog createAuditLog(Object entity, AuditLog.AuditOperation operation) {
        LoginUserInfo currentUser = AuditContext.getCurrentUser();

        return AuditLog.builder()
            .entityName(getEntityName(entity))
            .entityId(getEntityId(entity))
            .operation(operation)
            .userId(currentUser != null ? currentUser.getUserId() : null)
            .userNickname(AuditContext.getCurrentUserNickname())
            .reason(AuditContext.getCurrentOperation())
            .build();
    }

    private String getEntityName(Object entity) {
        if (entity instanceof AuditableEntity auditableEntity) {
            return auditableEntity.getEntityName();
        }
        return entity.getClass().getSimpleName();
    }

    private String getEntityId(Object entity) {
        if (entity instanceof AuditableEntity auditableEntity) {
            Object id = auditableEntity.getId();
            return id != null ? id.toString() : "null";
        }

        // Try to find ID field using reflection
        try {
            Field idField = findIdField(entity.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                Object id = idField.get(entity);
                return id != null ? id.toString() : "null";
            }
        } catch (Exception e) {
            log.warn("Failed to extract entity ID: {}", e.getMessage());
        }

        return "unknown";
    }

    private Field findIdField(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(jakarta.persistence.Id.class)) {
                return field;
            }
        }

        // Check superclass
        if (clazz.getSuperclass() != null) {
            return findIdField(clazz.getSuperclass());
        }

        return null;
    }

    /**
     * Get the original entity state for audit comparison
     * This method is called from the entity listener to capture the state before update
     */
    public Object getOriginalEntityState(Object entity) {
        return getOriginalEntity(entity);
    }

    private Object getOriginalEntity(Object entity) {
        try {
            if (entity instanceof AuditableEntity auditableEntity) {
                Object id = auditableEntity.getId();
                if (id != null) {
                    // Create a fresh query to get the original state from the database
                    // This bypasses the persistence context cache and uses a new transaction
                    String entityName = entity.getClass().getSimpleName();
                    String queryStr = "SELECT e FROM " + entityName + " e WHERE e.id = :id";

                    // Use a new EntityManager to avoid persistence context issues
                    EntityManager freshEntityManager = entityManager.getEntityManagerFactory().createEntityManager();
                    try {
                        return freshEntityManager.createQuery(queryStr, entity.getClass())
                                .setParameter("id", id)
                                .setHint("jakarta.persistence.cache.retrieveMode", "BYPASS")
                                .setHint("org.hibernate.cacheable", false)
                                .getSingleResult();
                    } finally {
                        freshEntityManager.close();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to retrieve original entity: {}", e.getMessage());
        }
        return null;
    }

    private List<AuditDetail> extractFieldDetails(Object entity, Object oldEntity, Object newEntity) {
        List<AuditDetail> details = new ArrayList<>();

        Field[] fields = getAllFields(entity.getClass());
        log.debug("Extracting field details from {} fields for entity: {}", fields.length, entity.getClass().getSimpleName());

        for (Field field : fields) {
            if (shouldExcludeField(field)) {
                log.debug("Excluding field: {} (reason: excluded)", field.getName());
                continue;
            }

            try {
                field.setAccessible(true);

                Object oldValue = oldEntity != null ? field.get(oldEntity) : null;
                Object newValue = newEntity != null ? field.get(newEntity) : null;

                log.debug("Field '{}': old='{}', new='{}', equal={}",
                         field.getName(), oldValue, newValue, Objects.equals(oldValue, newValue));

                // Skip if values are the same
                if (Objects.equals(oldValue, newValue)) {
                    continue;
                }

                AuditDetail detail = AuditDetail.builder()
                    .fieldName(field.getName())
                    .fieldType(field.getType().getSimpleName())
                    .oldValue(JsonUtils.toSafeString(oldValue, maxFieldLength))
                    .newValue(JsonUtils.toSafeString(newValue, maxFieldLength))
                    .isSensitive(isSensitiveField(field))
                    .build();

                details.add(detail);
                log.debug("Added audit detail for field: {}", field.getName());

            } catch (Exception e) {
                log.warn("Failed to extract field value for {}: {}", field.getName(), e.getMessage());
            }
        }

        log.debug("Extracted {} field details", details.size());
        return details;
    }

    private Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    private boolean shouldExcludeField(Field field) {
        String fieldName = field.getName().toLowerCase();

        // Exclude static and transient fields
        if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
            java.lang.reflect.Modifier.isTransient(field.getModifiers())) {
            return true;
        }

        // Exclude JPA annotations
        if (field.isAnnotationPresent(jakarta.persistence.Transient.class)) {
            return true;
        }

        // Exclude configured fields
        return excludedFields.contains(fieldName);
    }

    private boolean isSensitiveField(Field field) {
        String fieldName = field.getName().toLowerCase();
        return fieldName.contains("password") ||
               fieldName.contains("secret") ||
               fieldName.contains("token") ||
               fieldName.contains("key");
    }
}

package com.example.audit.service;

import com.example.audit.envers.AuditRevisionEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for querying Hibernate Envers audit data.
 * 
 * This service provides methods to retrieve audit history for entities,
 * including revision information, field changes, and user context.
 *
 * <AUTHOR> Agent
 */
@Service
@Slf4j
@Transactional(readOnly = true)
public class EnversAuditService {

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Get audit history for a specific entity
     */
    public <T> Page<AuditRevision<T>> getAuditHistory(Class<T> entityClass, Object entityId, Pageable pageable) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            // Get all revisions for the entity
            List<Number> revisions = auditReader.getRevisions(entityClass, entityId);
            
            // Calculate pagination
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), revisions.size());
            List<Number> pageRevisions = revisions.subList(start, end);
            
            // Get audit data for each revision
            List<AuditRevision<T>> auditRevisions = pageRevisions.stream()
                .map(revisionNumber -> {
                    T entity = auditReader.find(entityClass, entityId, revisionNumber);
                    AuditRevisionEntity revisionEntity = auditReader.findRevision(AuditRevisionEntity.class, revisionNumber);
                    
                    // Determine revision type
                    RevisionType revisionType = getRevisionType(auditReader, entityClass, entityId, revisionNumber);
                    
                    return new AuditRevision<>(entity, revisionEntity, revisionType);
                })
                .collect(Collectors.toList());
            
            return new PageImpl<>(auditRevisions, pageable, revisions.size());
            
        } catch (Exception e) {
            log.error("Failed to get audit history for {} with ID {}: {}", 
                     entityClass.getSimpleName(), entityId, e.getMessage());
            return Page.empty(pageable);
        }
    }

    /**
     * Get audit history for all entities of a specific type
     */
    public <T> Page<AuditRevision<T>> getAuditHistoryForEntityType(Class<T> entityClass, Pageable pageable) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .addOrder(AuditEntity.revisionNumber().desc());
            
            // Apply pagination
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
            
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            List<AuditRevision<T>> auditRevisions = results.stream()
                .map(result -> {
                    @SuppressWarnings("unchecked")
                    T entity = (T) result[0];
                    AuditRevisionEntity revisionEntity = (AuditRevisionEntity) result[1];
                    RevisionType revisionType = (RevisionType) result[2];
                    
                    return new AuditRevision<>(entity, revisionEntity, revisionType);
                })
                .collect(Collectors.toList());
            
            // Get total count
            long totalCount = getTotalAuditCount(entityClass);
            
            return new PageImpl<>(auditRevisions, pageable, totalCount);
            
        } catch (Exception e) {
            log.error("Failed to get audit history for entity type {}: {}", 
                     entityClass.getSimpleName(), e.getMessage());
            return Page.empty(pageable);
        }
    }

    /**
     * Get audit history within a date range
     */
    public <T> Page<AuditRevision<T>> getAuditHistoryByDateRange(
            Class<T> entityClass, 
            LocalDateTime startDate, 
            LocalDateTime endDate, 
            Pageable pageable) {
        
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            Date start = Date.from(startDate.atZone(ZoneId.systemDefault()).toInstant());
            Date end = Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant());
            
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .add(AuditEntity.revisionProperty("timestamp").ge(start.getTime()))
                .add(AuditEntity.revisionProperty("timestamp").le(end.getTime()))
                .addOrder(AuditEntity.revisionNumber().desc());
            
            // Apply pagination
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
            
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            List<AuditRevision<T>> auditRevisions = results.stream()
                .map(result -> {
                    @SuppressWarnings("unchecked")
                    T entity = (T) result[0];
                    AuditRevisionEntity revisionEntity = (AuditRevisionEntity) result[1];
                    RevisionType revisionType = (RevisionType) result[2];
                    
                    return new AuditRevision<>(entity, revisionEntity, revisionType);
                })
                .collect(Collectors.toList());
            
            // Get total count for date range
            long totalCount = getTotalAuditCountByDateRange(entityClass, start, end);
            
            return new PageImpl<>(auditRevisions, pageable, totalCount);
            
        } catch (Exception e) {
            log.error("Failed to get audit history by date range for {}: {}", 
                     entityClass.getSimpleName(), e.getMessage());
            return Page.empty(pageable);
        }
    }

    /**
     * Get audit history by user
     */
    public <T> Page<AuditRevision<T>> getAuditHistoryByUser(
            Class<T> entityClass, 
            String userNickname, 
            Pageable pageable) {
        
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .add(AuditEntity.revisionProperty("userNickname").eq(userNickname))
                .addOrder(AuditEntity.revisionNumber().desc());
            
            // Apply pagination
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
            
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            List<AuditRevision<T>> auditRevisions = results.stream()
                .map(result -> {
                    @SuppressWarnings("unchecked")
                    T entity = (T) result[0];
                    AuditRevisionEntity revisionEntity = (AuditRevisionEntity) result[1];
                    RevisionType revisionType = (RevisionType) result[2];
                    
                    return new AuditRevision<>(entity, revisionEntity, revisionType);
                })
                .collect(Collectors.toList());
            
            // Get total count for user
            long totalCount = getTotalAuditCountByUser(entityClass, userNickname);
            
            return new PageImpl<>(auditRevisions, pageable, totalCount);
            
        } catch (Exception e) {
            log.error("Failed to get audit history by user {} for {}: {}", 
                     userNickname, entityClass.getSimpleName(), e.getMessage());
            return Page.empty(pageable);
        }
    }

    /**
     * Get field changes between two revisions
     */
    public <T> Map<String, Object[]> getFieldChanges(Class<T> entityClass, Object entityId, 
                                                     Number fromRevision, Number toRevision) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            T fromEntity = auditReader.find(entityClass, entityId, fromRevision);
            T toEntity = auditReader.find(entityClass, entityId, toRevision);
            
            // This is a simplified implementation - in a real scenario, you'd use reflection
            // or a more sophisticated approach to compare field values
            return Map.of(); // Placeholder
            
        } catch (Exception e) {
            log.error("Failed to get field changes for {} with ID {} between revisions {} and {}: {}", 
                     entityClass.getSimpleName(), entityId, fromRevision, toRevision, e.getMessage());
            return Map.of();
        }
    }

    private RevisionType getRevisionType(AuditReader auditReader, Class<?> entityClass, 
                                       Object entityId, Number revisionNumber) {
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .add(AuditEntity.id().eq(entityId))
                .add(AuditEntity.revisionNumber().eq(revisionNumber));
            
            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();
            
            if (!results.isEmpty()) {
                return (RevisionType) results.get(0)[2];
            }
        } catch (Exception e) {
            log.debug("Could not determine revision type: {}", e.getMessage());
        }
        
        return RevisionType.MOD; // Default fallback
    }

    private <T> long getTotalAuditCount(Class<T> entityClass) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, true, false);
            
            @SuppressWarnings("unchecked")
            List<Object> results = query.getResultList();
            return results.size();
        } catch (Exception e) {
            log.debug("Could not get total audit count: {}", e.getMessage());
            return 0;
        }
    }

    private <T> long getTotalAuditCountByDateRange(Class<T> entityClass, Date start, Date end) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, true, false)
                .add(AuditEntity.revisionProperty("timestamp").ge(start.getTime()))
                .add(AuditEntity.revisionProperty("timestamp").le(end.getTime()));
            
            @SuppressWarnings("unchecked")
            List<Object> results = query.getResultList();
            return results.size();
        } catch (Exception e) {
            log.debug("Could not get total audit count by date range: {}", e.getMessage());
            return 0;
        }
    }

    private <T> long getTotalAuditCountByUser(Class<T> entityClass, String userNickname) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        
        try {
            AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, true, false)
                .add(AuditEntity.revisionProperty("userNickname").eq(userNickname));
            
            @SuppressWarnings("unchecked")
            List<Object> results = query.getResultList();
            return results.size();
        } catch (Exception e) {
            log.debug("Could not get total audit count by user: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * Data class representing an audit revision
     */
    public static class AuditRevision<T> {
        private final T entity;
        private final AuditRevisionEntity revisionEntity;
        private final RevisionType revisionType;

        public AuditRevision(T entity, AuditRevisionEntity revisionEntity, RevisionType revisionType) {
            this.entity = entity;
            this.revisionEntity = revisionEntity;
            this.revisionType = revisionType;
        }

        public T getEntity() { return entity; }
        public AuditRevisionEntity getRevisionEntity() { return revisionEntity; }
        public RevisionType getRevisionType() { return revisionType; }
    }
}

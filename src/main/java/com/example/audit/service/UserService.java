package com.example.audit.service;

import com.example.audit.entity.User;
import com.example.audit.listener.AuditEntityListener;
import com.example.audit.repository.UserRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service class for User entity operations.
 * All operations are automatically audited through the AuditableEntity base class.
 *
 * <AUTHOR> Agent
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Create a new user
     *
     * @param user the user to create
     * @return the created user
     * @throws IllegalArgumentException if username or email already exists
     */
    public User createUser(User user) {
        log.debug("Creating user: {}", user.getUsername());

        // Validate uniqueness
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new IllegalArgumentException("Username already exists: " + user.getUsername());
        }

        if (userRepository.existsByEmail(user.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + user.getEmail());
        }

        User savedUser = userRepository.save(user);
        log.info("Created user: {} with ID: {}", savedUser.getUsername(), savedUser.getId());

        return savedUser;
    }

    /**
     * Update an existing user
     *
     * @param id the user ID
     * @param userUpdates the user updates
     * @return the updated user
     * @throws IllegalArgumentException if user not found or validation fails
     */
    @Transactional
    public User updateUser(Long id, User userUpdates) {
        log.debug("Updating user with ID: {}", id);

        // First, get the original entity state using a fresh EntityManager to bypass cache
        User originalUserForAudit = getOriginalUserForAudit(id);

        log.debug("Captured original state - username: {}, notes: {}",
                  originalUserForAudit.getUsername(), originalUserForAudit.getNotes());

        // Now get the entity for modification
        User existingUser = userRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + id));

        // Validate uniqueness for changed fields
        if (!existingUser.getUsername().equals(userUpdates.getUsername()) &&
            userRepository.existsByUsername(userUpdates.getUsername())) {
            throw new IllegalArgumentException("Username already exists: " + userUpdates.getUsername());
        }

        if (!existingUser.getEmail().equals(userUpdates.getEmail()) &&
            userRepository.existsByEmail(userUpdates.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + userUpdates.getEmail());
        }

        // Store the original state BEFORE making any changes
        AuditEntityListener.storeOriginalEntityState(existingUser, originalUserForAudit);

        // Update fields
        existingUser.setUsername(userUpdates.getUsername());
        existingUser.setEmail(userUpdates.getEmail());
        existingUser.setFirstName(userUpdates.getFirstName());
        existingUser.setLastName(userUpdates.getLastName());
        existingUser.setPhoneNumber(userUpdates.getPhoneNumber());
        existingUser.setDepartment(userUpdates.getDepartment());
        existingUser.setJobTitle(userUpdates.getJobTitle());
        existingUser.setStatus(userUpdates.getStatus());
        existingUser.setNotes(userUpdates.getNotes());

        log.debug("Modified user after changes - username: {}, notes: {}",
                  existingUser.getUsername(), existingUser.getNotes());
        log.debug("Audit comparison - original username: {}, new username: {}",
                  originalUserForAudit.getUsername(), existingUser.getUsername());

        User savedUser = userRepository.save(existingUser);
        log.info("Updated user: {} with ID: {}", savedUser.getUsername(), savedUser.getId());

        return savedUser;
    }

    /**
     * Get the original user state for audit purposes using a fresh EntityManager
     * to bypass the persistence context cache
     */
    private User getOriginalUserForAudit(Long id) {
        // Create a fresh EntityManager to bypass the persistence context cache
        EntityManager freshEntityManager = entityManager.getEntityManagerFactory().createEntityManager();
        try {
            User originalUser = freshEntityManager.find(User.class, id);
            if (originalUser == null) {
                throw new IllegalArgumentException("User not found with ID: " + id);
            }

            // Create a detached copy to avoid any persistence context issues
            return User.builder()
                .id(originalUser.getId())
                .username(originalUser.getUsername())
                .email(originalUser.getEmail())
                .firstName(originalUser.getFirstName())
                .lastName(originalUser.getLastName())
                .phoneNumber(originalUser.getPhoneNumber())
                .department(originalUser.getDepartment())
                .jobTitle(originalUser.getJobTitle())
                .status(originalUser.getStatus())
                .notes(originalUser.getNotes())
                .build();
        } finally {
            freshEntityManager.close();
        }
    }

    /**
     * Delete a user (soft delete)
     *
     * @param id the user ID
     * @throws IllegalArgumentException if user not found
     */
    public void deleteUser(Long id) {
        log.debug("Deleting user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + id));

        user.markAsDeleted();
        userRepository.save(user);

        log.info("Deleted user: {} with ID: {}", user.getUsername(), user.getId());
    }

    /**
     * Get user by ID
     *
     * @param id the user ID
     * @return the user
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * Get user by username
     *
     * @param username the username
     * @return the user
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * Get user by email
     *
     * @param email the email
     * @return the user
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * Get all users with pagination
     *
     * @param pageable pagination information
     * @return page of users
     */
    @Transactional(readOnly = true)
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    /**
     * Search users
     *
     * @param searchTerm the search term
     * @param pageable pagination information
     * @return page of users
     */
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return userRepository.searchUsers(searchTerm, pageable);
    }

    /**
     * Get users by status
     *
     * @param status the user status
     * @param pageable pagination information
     * @return page of users
     */
    @Transactional(readOnly = true)
    public Page<User> getUsersByStatus(User.UserStatus status, Pageable pageable) {
        return userRepository.findByStatus(status, pageable);
    }

    /**
     * Get users by department
     *
     * @param department the department
     * @param pageable pagination information
     * @return page of users
     */
    @Transactional(readOnly = true)
    public Page<User> getUsersByDepartment(String department, Pageable pageable) {
        return userRepository.findByDepartment(department, pageable);
    }
}

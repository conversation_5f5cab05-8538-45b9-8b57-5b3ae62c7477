package com.example.audit.entity;

import com.example.audit.listener.AuditEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;

/**
 * Abstract base class for entities that require auditing capabilities.
 *
 * This class provides:
 * - Automatic creation and modification timestamps
 * - User tracking for who created and last modified the entity
 * - Integration with Hibernate Envers for comprehensive audit tracking
 *
 * Entities extending this class will automatically have their changes tracked
 * using Hibernate Envers with field-level granularity and revision history.
 *
 * <AUTHOR> Agent
 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@Data
public abstract class AuditableEntity {

    /**
     * Timestamp when the entity was created
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * User who created the entity
     */
    @CreatedBy
    @Column(name = "created_by", length = 100, updatable = false)
    private String createdBy;

    /**
     * Timestamp when the entity was last modified
     */
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * User who last modified the entity
     */
    @LastModifiedBy
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * Version field for optimistic locking
     */
    @Version
    @Column(name = "version")
    private Long version;

    /**
     * Soft delete flag
     */
    @Column(name = "deleted")
    private Boolean deleted = false;

    /**
     * Get the unique identifier of this entity.
     * Subclasses must implement this method to return their primary key.
     *
     * @return the unique identifier of the entity
     */
    public abstract Object getId();

    /**
     * Get the entity name for audit logging.
     * By default, returns the simple class name.
     *
     * @return the entity name for audit purposes
     */
    @JsonIgnore
    public String getEntityName() {
        return this.getClass().getSimpleName();
    }

    /**
     * Check if this entity is new (not yet persisted)
     *
     * @return true if the entity is new, false otherwise
     */
    @JsonIgnore
    public boolean isNew() {
        return getId() == null;
    }

    /**
     * Mark this entity as deleted (soft delete)
     */
    public void markAsDeleted() {
        this.deleted = true;
    }

    /**
     * Check if this entity is deleted
     *
     * @return true if the entity is marked as deleted
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(deleted);
    }
}

package com.example.audit.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.hibernate6.Hibernate6Module;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson configuration for handling Hibernate entities, proxies, and Java 8 time types.
 *
 * This configuration ensures that:
 * - Hibernate entities can be properly serialized to JSON
 * - Lazy-loaded proxies are handled correctly
 * - Java 8 time types (LocalDateTime, LocalDate, etc.) are serialized properly
 * - Prevents serialization errors with Hibernate-specific objects
 *
 * <AUTHOR> Agent
 */
@Configuration
public class JacksonConfig {

    /**
     * Configure ObjectMapper with Hibernate6Module and JavaTimeModule
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // Register JavaTimeModule for Java 8 time types (LocalDateTime, LocalDate, etc.)
        mapper.registerModule(new JavaTimeModule());

        // Register Hibernate6Module to handle Hibernate proxies
        Hibernate6Module hibernate6Module = new Hibernate6Module();

        // Configure the module to handle lazy loading
        hibernate6Module.disable(Hibernate6Module.Feature.USE_TRANSIENT_ANNOTATION);
        hibernate6Module.enable(Hibernate6Module.Feature.FORCE_LAZY_LOADING);
        hibernate6Module.enable(Hibernate6Module.Feature.SERIALIZE_IDENTIFIER_FOR_LAZY_NOT_LOADED_OBJECTS);

        mapper.registerModule(hibernate6Module);

        // Configure serialization features
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        return mapper;
    }
}

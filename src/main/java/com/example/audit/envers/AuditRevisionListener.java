package com.example.audit.envers;

import com.example.audit.dto.LoginUserInfo;
import com.example.audit.util.AuditContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.RevisionListener;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Hibernate Envers revision listener that automatically populates audit revision
 * information with user context, IP address, and other relevant details.
 *
 * This listener is called by <PERSON><PERSON> whenever a new revision is created,
 * allowing us to capture additional audit information beyond the default
 * revision ID and timestamp.
 *
 * <AUTHOR> Agent
 */
@Slf4j
public class AuditRevisionListener implements RevisionListener {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void newRevision(Object revisionEntity) {
        if (revisionEntity instanceof AuditRevisionEntity auditRevision) {
            populateRevisionInfo(auditRevision);
        }
    }

    /**
     * Populate the revision entity with audit information from the current context
     */
    private void populateRevisionInfo(AuditRevisionEntity revision) {
        try {
            // Get user context from AuditContext
            LoginUserInfo userInfo = AuditContext.getCurrentUser();
            if (userInfo != null) {
                revision.setUserId(userInfo.getUserId());
                revision.setUserNickname(userInfo.getNickName());
                revision.setFromType(userInfo.getFromType());
            } else {
                // Fallback to anonymous user
                revision.setUserNickname("anonymous");
                revision.setFromType("unknown");
            }

            // Get HTTP request information if available
            populateRequestInfo(revision);

            // Create context JSON
            Map<String, Object> context = createContextMap(userInfo);
            if (!context.isEmpty()) {
                revision.setContext(objectMapper.writeValueAsString(context));
            }

            log.debug("Populated Envers revision: user={}, ip={}, fromType={}",
                     revision.getUserNickname(), revision.getIpAddress(), revision.getFromType());

        } catch (Exception e) {
            log.warn("Failed to populate revision info: {}", e.getMessage());
            // Set minimal fallback information
            revision.setUserNickname("system");
            revision.setFromType("error");
        }
    }

    /**
     * Populate request-specific information from the current HTTP request
     */
    private void populateRequestInfo(AuditRevisionEntity revision) {
        try {
            ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // Get IP address
                String ipAddress = getClientIpAddress(request);
                revision.setIpAddress(ipAddress);

                // Get user agent
                String userAgent = request.getHeader("User-Agent");
                if (userAgent != null && userAgent.length() > 500) {
                    userAgent = userAgent.substring(0, 500);
                }
                revision.setUserAgent(userAgent);
            }
        } catch (Exception e) {
            log.debug("Could not get request information: {}", e.getMessage());
        }
    }

    /**
     * Get the client IP address from the request, handling proxy headers
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR"
        };

        for (String header : headerNames) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * Create a context map with additional audit information
     */
    private Map<String, Object> createContextMap(LoginUserInfo userInfo) {
        Map<String, Object> context = new HashMap<>();

        if (userInfo != null) {
            context.put("userId", userInfo.getUserId());
            context.put("nickName", userInfo.getNickName());
            context.put("fromType", userInfo.getFromType());
        }

        // Add timestamp
        context.put("timestamp", System.currentTimeMillis());

        return context;
    }
}

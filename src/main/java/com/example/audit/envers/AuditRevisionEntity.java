package com.example.audit.envers;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;

/**
 * Custom revision entity for Hibernate Envers that captures additional audit information
 * including user context, IP address, and user agent.
 * 
 * This entity extends DefaultRevisionEntity to include custom audit fields
 * that are automatically populated by the AuditRevisionListener.
 *
 * <AUTHOR> Agent
 */
@Entity
@Table(name = "audit_revisions")
@RevisionEntity(AuditRevisionListener.class)
@Data
@EqualsAndHashCode(callSuper = true)
public class AuditRevisionEntity extends DefaultRevisionEntity {

    /**
     * User ID who performed the operation
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * User nickname/username who performed the operation
     */
    @Column(name = "user_nickname", length = 100)
    private String userNickname;

    /**
     * IP address from which the operation was performed
     */
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    /**
     * User agent string from the request
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * Source of the operation (e.g., "web", "api", "system")
     */
    @Column(name = "from_type", length = 50)
    private String fromType;

    /**
     * Additional context information as JSON
     */
    @Column(name = "context", columnDefinition = "TEXT")
    private String context;

    /**
     * Reason for the operation (optional)
     */
    @Column(name = "reason", length = 500)
    private String reason;
}

package com.example.audit.repository;

import com.example.audit.entity.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for AuditLog entities.
 * Provides methods for querying audit records with various filters.
 *
 * <AUTHOR> Agent
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {

    /**
     * Find audit log by ID with details eagerly fetched
     *
     * @param id the audit log ID
     * @return optional audit log with details
     */
    @Query("SELECT a FROM AuditLog a LEFT JOIN FETCH a.details WHERE a.id = :id")
    Optional<AuditLog> findByIdWithDetails(@Param("id") Long id);

    /**
     * Find audit logs for a specific entity
     *
     * @param entityName the entity class name
     * @param entityId the entity ID
     * @param pageable pagination information
     * @return page of audit logs
     */
    @Query("SELECT a FROM AuditLog a LEFT JOIN FETCH a.details WHERE a.entityName = :entityName AND a.entityId = :entityId ORDER BY a.timestamp DESC")
    Page<AuditLog> findByEntityNameAndEntityIdOrderByTimestampDesc(
        @Param("entityName") String entityName, @Param("entityId") String entityId, Pageable pageable);

    /**
     * Find audit logs for a specific entity type
     *
     * @param entityName the entity class name
     * @param pageable pagination information
     * @return page of audit logs
     */
    Page<AuditLog> findByEntityNameOrderByTimestampDesc(
        String entityName, Pageable pageable);

    /**
     * Find audit logs by user
     *
     * @param userId the user ID
     * @param pageable pagination information
     * @return page of audit logs
     */
    Page<AuditLog> findByUserIdOrderByTimestampDesc(
        Long userId, Pageable pageable);

    /**
     * Find audit logs by user nickname
     *
     * @param userNickname the user nickname
     * @param pageable pagination information
     * @return page of audit logs
     */
    Page<AuditLog> findByUserNicknameOrderByTimestampDesc(
        String userNickname, Pageable pageable);

    /**
     * Find audit logs by operation type
     *
     * @param operation the operation type
     * @param pageable pagination information
     * @return page of audit logs
     */
    @Query("SELECT a FROM AuditLog a LEFT JOIN FETCH a.details WHERE a.operation = :operation ORDER BY a.timestamp DESC")
    Page<AuditLog> findByOperationOrderByTimestampDesc(
        @Param("operation") AuditLog.AuditOperation operation, Pageable pageable);

    /**
     * Find audit logs within a date range
     *
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @param pageable pagination information
     * @return page of audit logs
     */
    Page<AuditLog> findByTimestampBetweenOrderByTimestampDesc(
        LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find audit logs with complex filtering
     *
     * @param entityName entity name filter (optional)
     * @param entityId entity ID filter (optional)
     * @param userId user ID filter (optional)
     * @param operation operation filter (optional)
     * @param startDate start date filter (optional)
     * @param endDate end date filter (optional)
     * @param pageable pagination information
     * @return page of audit logs
     */
    @Query("SELECT a FROM AuditLog a LEFT JOIN FETCH a.details WHERE " +
           "(:entityName IS NULL OR a.entityName = :entityName) AND " +
           "(:entityId IS NULL OR a.entityId = :entityId) AND " +
           "(:userId IS NULL OR a.userId = :userId) AND " +
           "(:operation IS NULL OR a.operation = :operation) AND " +
           "(:startDate IS NULL OR a.timestamp >= :startDate) AND " +
           "(:endDate IS NULL OR a.timestamp <= :endDate) " +
           "ORDER BY a.timestamp DESC")
    Page<AuditLog> findWithFilters(
        @Param("entityName") String entityName,
        @Param("entityId") String entityId,
        @Param("userId") Long userId,
        @Param("operation") AuditLog.AuditOperation operation,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        Pageable pageable);

    /**
     * Count audit logs for a specific entity
     *
     * @param entityName the entity class name
     * @param entityId the entity ID
     * @return count of audit logs
     */
    long countByEntityNameAndEntityId(String entityName, String entityId);

    /**
     * Find recent audit logs for dashboard
     *
     * @param limit maximum number of records
     * @return list of recent audit logs
     */
    @Query("SELECT a FROM AuditLog a LEFT JOIN FETCH a.details ORDER BY a.timestamp DESC")
    List<AuditLog> findRecentAuditLogs(Pageable pageable);

    /**
     * Delete old audit logs for cleanup
     *
     * @param cutoffDate date before which to delete logs
     * @return number of deleted records
     */
    @Query("DELETE FROM AuditLog a WHERE a.timestamp < :cutoffDate")
    int deleteOldAuditLogs(@Param("cutoffDate") LocalDateTime cutoffDate);
}

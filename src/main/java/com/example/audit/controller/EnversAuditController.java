package com.example.audit.controller;

import com.example.audit.entity.Product;
import com.example.audit.entity.User;
import com.example.audit.service.EnversAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * REST controller for accessing Hibernate Envers audit data.
 * 
 * This controller provides endpoints to query audit history for entities
 * using Hibernate Envers, offering more comprehensive audit tracking
 * than the custom audit system.
 *
 * <AUTHOR> Agent
 */
@RestController
@RequestMapping("/api/envers-audit")
@RequiredArgsConstructor
@Slf4j
public class EnversAuditController {

    private final EnversAuditService enversAuditService;

    /**
     * Get audit history for a specific user
     */
    @GetMapping("/users/{id}")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<User>>> getUserAuditHistory(
            @PathVariable Long id,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for user ID: {}", id);
        
        Page<EnversAuditService.AuditRevision<User>> auditHistory = 
            enversAuditService.getAuditHistory(User.class, id, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history for a specific product
     */
    @GetMapping("/products/{id}")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<Product>>> getProductAuditHistory(
            @PathVariable Long id,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for product ID: {}", id);
        
        Page<EnversAuditService.AuditRevision<Product>> auditHistory = 
            enversAuditService.getAuditHistory(Product.class, id, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history for all users
     */
    @GetMapping("/users")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<User>>> getAllUsersAuditHistory(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for all users");
        
        Page<EnversAuditService.AuditRevision<User>> auditHistory = 
            enversAuditService.getAuditHistoryForEntityType(User.class, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history for all products
     */
    @GetMapping("/products")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<Product>>> getAllProductsAuditHistory(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for all products");
        
        Page<EnversAuditService.AuditRevision<Product>> auditHistory = 
            enversAuditService.getAuditHistoryForEntityType(Product.class, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history by date range for users
     */
    @GetMapping("/users/by-date")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<User>>> getUserAuditHistoryByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for users between {} and {}", startDate, endDate);
        
        Page<EnversAuditService.AuditRevision<User>> auditHistory = 
            enversAuditService.getAuditHistoryByDateRange(User.class, startDate, endDate, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history by date range for products
     */
    @GetMapping("/products/by-date")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<Product>>> getProductAuditHistoryByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for products between {} and {}", startDate, endDate);
        
        Page<EnversAuditService.AuditRevision<Product>> auditHistory = 
            enversAuditService.getAuditHistoryByDateRange(Product.class, startDate, endDate, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history by user for users
     */
    @GetMapping("/users/by-user")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<User>>> getUserAuditHistoryByUser(
            @RequestParam String userNickname,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for users modified by: {}", userNickname);
        
        Page<EnversAuditService.AuditRevision<User>> auditHistory = 
            enversAuditService.getAuditHistoryByUser(User.class, userNickname, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit history by user for products
     */
    @GetMapping("/products/by-user")
    public ResponseEntity<Page<EnversAuditService.AuditRevision<Product>>> getProductAuditHistoryByUser(
            @RequestParam String userNickname,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.debug("Getting Envers audit history for products modified by: {}", userNickname);
        
        Page<EnversAuditService.AuditRevision<Product>> auditHistory = 
            enversAuditService.getAuditHistoryByUser(Product.class, userNickname, pageable);
        
        return ResponseEntity.ok(auditHistory);
    }

    /**
     * Get audit statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<AuditStats> getAuditStats() {
        log.debug("Getting Envers audit statistics");
        
        // This is a placeholder - you would implement actual statistics gathering
        AuditStats stats = new AuditStats();
        stats.setTotalUserRevisions(0L);
        stats.setTotalProductRevisions(0L);
        stats.setMessage("Envers audit statistics - implement as needed");
        
        return ResponseEntity.ok(stats);
    }

    /**
     * Data class for audit statistics
     */
    public static class AuditStats {
        private Long totalUserRevisions;
        private Long totalProductRevisions;
        private String message;

        // Getters and setters
        public Long getTotalUserRevisions() { return totalUserRevisions; }
        public void setTotalUserRevisions(Long totalUserRevisions) { this.totalUserRevisions = totalUserRevisions; }
        
        public Long getTotalProductRevisions() { return totalProductRevisions; }
        public void setTotalProductRevisions(Long totalProductRevisions) { this.totalProductRevisions = totalProductRevisions; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}

package com.example.audit.controller;

import com.example.audit.entity.User;
import com.example.audit.service.UserService;
import com.example.audit.service.AuditService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for User entity operations.
 * All operations are automatically audited.
 *
 * <AUTHOR> Agent
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;
    private final AuditService auditService;

    /**
     * Create a new user
     *
     * @param user the user to create
     * @return the created user
     */
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody User user) {
        log.debug("Creating user: {}", user.getUsername());

        try {
            User createdUser = userService.createUser(user);

            // Manually trigger audit log for dashboard
            try {
                auditService.auditCreate(createdUser);
                log.debug("Manually triggered audit log for user creation: {}", createdUser.getUsername());
            } catch (Exception auditError) {
                log.warn("Failed to create manual audit log: {}", auditError.getMessage());
            }

            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (IllegalArgumentException e) {
            log.warn("Failed to create user: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get user by ID
     *
     * @param id the user ID
     * @return the user
     */
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        log.debug("Getting user by ID: {}", id);

        Optional<User> user = userService.getUserById(id);
        return user.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Update user
     *
     * @param id the user ID
     * @param user the user updates
     * @return the updated user
     */
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @Valid @RequestBody User user) {
        log.debug("Updating user with ID: {}", id);

        try {
            User updatedUser = userService.updateUser(id, user);

            // Manually trigger audit log for dashboard
            try {
                auditService.auditUpdate(updatedUser);
                log.debug("Manually triggered audit log for user update: {}", updatedUser.getUsername());
            } catch (Exception auditError) {
                log.warn("Failed to create manual audit log: {}", auditError.getMessage());
            }

            return ResponseEntity.ok(updatedUser);
        } catch (IllegalArgumentException e) {
            log.warn("Failed to update user: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Delete user
     *
     * @param id the user ID
     * @return no content response
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        log.debug("Deleting user with ID: {}", id);

        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            log.warn("Failed to delete user: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get all users with pagination
     *
     * @param page page number (0-based)
     * @param size page size
     * @param sort sort criteria
     * @return page of users
     */
    @GetMapping
    public ResponseEntity<Page<User>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {

        log.debug("Getting all users - page: {}, size: {}, sort: {}", page, size, sort);

        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        Page<User> users = userService.getAllUsers(pageable);

        return ResponseEntity.ok(users);
    }

    /**
     * Search users
     *
     * @param q search query
     * @param page page number (0-based)
     * @param size page size
     * @return page of users
     */
    @GetMapping("/search")
    public ResponseEntity<Page<User>> searchUsers(
            @RequestParam String q,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.debug("Searching users with query: {}", q);

        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName", "lastName"));
        Page<User> users = userService.searchUsers(q, pageable);

        return ResponseEntity.ok(users);
    }

    /**
     * Get users by status
     *
     * @param status the user status
     * @param page page number (0-based)
     * @param size page size
     * @return page of users
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<Page<User>> getUsersByStatus(
            @PathVariable User.UserStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.debug("Getting users by status: {}", status);

        Pageable pageable = PageRequest.of(page, size, Sort.by("firstName", "lastName"));
        Page<User> users = userService.getUsersByStatus(status, pageable);

        return ResponseEntity.ok(users);
    }

    /**
     * Generate sample audit logs for dashboard demonstration
     *
     * @return success message
     */
    @PostMapping("/generate-audit-logs")
    public ResponseEntity<Map<String, String>> generateAuditLogs() {
        log.debug("Generating sample audit logs for dashboard");

        try {
            // Get some existing users to create audit logs for
            List<User> users = userService.getAllUsers(PageRequest.of(0, 5)).getContent();

            for (User user : users) {
                // Create sample CREATE audit log
                auditService.auditCreate(user);

                // Create sample UPDATE audit log
                auditService.auditUpdate(user);

                log.debug("Generated audit logs for user: {}", user.getUsername());
            }

            return ResponseEntity.ok(Map.of("message", "Sample audit logs generated successfully"));
        } catch (Exception e) {
            log.error("Failed to generate audit logs: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to generate audit logs"));
        }
    }
}

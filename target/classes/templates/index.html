<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::content}, ~{::scripts})}">
<head>
    <title>JPA Auditing Demo - Home</title>
</head>
<body>
    <div th:fragment="content">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">JPA Auditing Demo</h1>
                <p class="text-muted">Comprehensive field-level change tracking system</p>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-history fa-2x text-primary mb-3"></i>
                        <h5 class="card-title">Field-Level Tracking</h5>
                        <p class="card-text">Capture granular changes with before/after values</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-user-shield fa-2x text-success mb-3"></i>
                        <h5 class="card-title">User Context</h5>
                        <p class="card-text">Track who made changes with user information</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                        <h5 class="card-title">Temporal Auditing</h5>
                        <p class="card-text">Precise timestamps with timezone support</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-cogs fa-2x text-info mb-3"></i>
                        <h5 class="card-title">Enterprise Ready</h5>
                        <p class="card-text">Reusable across 80+ microservices</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>Users Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Total Users:</span>
                            <span class="badge bg-primary fs-6" th:text="${totalUsers}">0</span>
                        </div>
                        <a th:href="@{/users}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View All Users
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Recent Activity:</span>
                            <span class="badge bg-success fs-6">Active</span>
                        </div>
                        <a th:href="@{/audit}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-chart-line me-1"></i>View Audit Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>Recent Users
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(recentUsers)}" class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>No users found. Create your first user to see audit tracking in action!</p>
                    <button class="btn btn-primary" onclick="createSampleUser()">
                        <i class="fas fa-plus me-1"></i>Create Sample User
                    </button>
                </div>

                <div th:if="${!#lists.isEmpty(recentUsers)}" class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Department</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="user : ${recentUsers}">
                                <td>
                                    <strong th:text="${user.fullName}">John Doe</strong>
                                    <br>
                                    <small class="text-muted" th:text="${user.username}">johndoe</small>
                                </td>
                                <td th:text="${user.email}"><EMAIL></td>
                                <td>
                                    <span th:if="${user.department}" th:text="${user.department}">IT</span>
                                    <span th:unless="${user.department}" class="text-muted">-</span>
                                </td>
                                <td>
                                    <span class="badge"
                                          th:classappend="${user.status.name() == 'ACTIVE'} ? 'bg-success' : 'bg-secondary'"
                                          th:text="${user.status}">ACTIVE</span>
                                </td>
                                <td>
                                    <small th:text="${#temporals.format(user.createdAt, 'MMM dd, yyyy HH:mm')}">
                                        Jan 01, 2024 10:00
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a th:href="@{/api/users/{id}(id=${user.id})}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-info btn-sm"
                                                th:onclick="|AuditDemo.showAuditTrail('User', '${user.id}')|">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Demo Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>How to Test Audit Functionality
                </h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Create a User:</strong> Use the API or create sample data to add users</li>
                    <li><strong>Modify User Data:</strong> Update user information to see field-level changes</li>
                    <li><strong>View Audit Trail:</strong> Click the history button to see detailed change tracking</li>
                    <li><strong>Test User Context:</strong> Send requests with the <code>x-user</code> header to track who made changes</li>
                </ol>

                <div class="alert alert-info mt-3">
                    <strong>API Endpoints:</strong>
                    <ul class="mb-0">
                        <li><code>GET /api/users</code> - List users</li>
                        <li><code>POST /api/users</code> - Create user</li>
                        <li><code>PUT /api/users/{id}</code> - Update user</li>
                        <li><code>GET /api/audit/entity/User/{id}</code> - Get audit trail</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div th:fragment="scripts">
        <script>
            function createSampleUser() {
                const sampleUser = {
                    username: 'demo_user_' + Date.now(),
                    email: 'demo' + Date.now() + '@example.com',
                    firstName: 'Demo',
                    lastName: 'User',
                    department: 'IT',
                    jobTitle: 'Software Developer',
                    status: 'ACTIVE'
                };

                fetch('/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-user': JSON.stringify({
                            userId: 1,
                            nickName: 'demo_admin',
                            fromType: 'web'
                        })
                    },
                    body: JSON.stringify(sampleUser)
                })
                .then(response => response.json())
                .then(data => {
                    location.reload();
                })
                .catch(error => {
                    console.error('Error creating user:', error);
                    alert('Failed to create sample user');
                });
            }
        </script>
    </div>
</body>
</html>

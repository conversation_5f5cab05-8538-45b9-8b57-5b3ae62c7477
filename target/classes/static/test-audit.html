<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audit Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Audit API Test</h1>
        
        <div class="row mb-3">
            <div class="col">
                <button class="btn btn-primary" onclick="testAPI()">Test API</button>
                <button class="btn btn-success" onclick="loadAuditData()">Load Audit Data</button>
            </div>
        </div>
        
        <div class="row">
            <div class="col">
                <div id="results" class="alert alert-info">
                    Click "Test API" to test the audit API endpoint.
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col">
                <table class="table table-striped" id="auditTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Entity</th>
                            <th>Operation</th>
                            <th>User</th>
                            <th>Timestamp</th>
                        </tr>
                    </thead>
                    <tbody id="auditTableBody">
                        <tr>
                            <td colspan="5" class="text-center">No data loaded</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function testAPI() {
            console.log('Testing API...');
            document.getElementById('results').innerHTML = 'Testing API...';
            
            fetch('/audit-demo/api/audit?page=0&size=1&_t=' + Date.now())
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    document.getElementById('results').innerHTML = 
                        '<strong>Success!</strong> Found ' + (data.totalElements || 0) + ' audit logs. ' +
                        'First log: ' + (data.content && data.content[0] ? JSON.stringify(data.content[0]) : 'None');
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('results').innerHTML = 
                        '<strong>Error:</strong> ' + error.message;
                });
        }
        
        function loadAuditData() {
            console.log('Loading audit data...');
            document.getElementById('auditTableBody').innerHTML = 
                '<tr><td colspan="5" class="text-center">Loading...</td></tr>';
            
            fetch('/audit-demo/api/audit?page=0&size=5&_t=' + Date.now())
                .then(response => response.json())
                .then(data => {
                    console.log('Audit data:', data);
                    const tbody = document.getElementById('auditTableBody');
                    
                    if (!data.content || data.content.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="5" class="text-center">No audit logs found</td></tr>';
                        return;
                    }
                    
                    let html = '';
                    data.content.forEach(log => {
                        html += `
                            <tr>
                                <td>${log.id}</td>
                                <td>${log.entityName}</td>
                                <td><span class="badge bg-success">${log.operation}</span></td>
                                <td>${log.userNickname || log.userId || 'System'}</td>
                                <td>${new Date(log.timestamp).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                    tbody.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading audit data:', error);
                    document.getElementById('auditTableBody').innerHTML = 
                        '<tr><td colspan="5" class="text-center text-danger">Error: ' + error.message + '</td></tr>';
                });
        }
    </script>
</body>
</html>
